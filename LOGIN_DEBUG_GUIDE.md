# Login 400 Error Debugging Guide

## 🔍 **Debugging Steps**

### 1. **Frontend Logging (Now Active)**
- Open browser console (F12)
- Look for detailed logs starting with 📤, 📥, 🔐, ❌
- Click "Debug Network" button in bottom-right corner for real-time network logs

### 2. **Common 400 Error Causes**

#### **A. Request Format Issues**
- **Wrong Content-Type**: Should be `application/json`
- **Missing Required Fields**: Check if backend expects specific field names
- **Field Name Mismatch**: Frontend sends `username`, backend expects `email`?

#### **B. Data Validation Issues**
- **Email Format**: If backend validates email format strictly
- **Password Requirements**: Minimum length, special characters, etc.
- **Extra Fields**: Backend rejects unexpected fields

#### **C. CORS Issues**
- **Preflight Requests**: OPTIONS request failing
- **Headers**: Missing or incorrect CORS headers

### 3. **What to Look For in Console**

#### **Request Details (📤)**
```
📤 API Request: {
  method: "POST",
  url: "/auth/login/",
  fullURL: "http://127.0.0.1:8000/api/v1/auth/login/",
  isPublicEndpoint: true,
  hasAuthHeader: false,
  data: { username: "...", password: "..." }
}
```

#### **Response Details (📥)**
```
📥 API Error Response: {
  status: 400,
  data: { /* This should show the actual error */ }
}
```

### 4. **Backend Debugging**

#### **Django Settings to Check**
1. **CORS Configuration**:
   ```python
   CORS_ALLOWED_ORIGINS = [
       "http://localhost:3000",
   ]
   ```

2. **API Endpoint**:
   ```python
   # Should be accessible at /api/v1/auth/login/
   ```

3. **Serializer Validation**:
   ```python
   # Check what fields are required/expected
   ```

#### **Add Django Logging**
Add this to your Django view temporarily:
```python
import logging
logger = logging.getLogger(__name__)

def login_view(request):
    logger.info(f"Login request data: {request.data}")
    logger.info(f"Content-Type: {request.content_type}")
    logger.info(f"Headers: {dict(request.headers)}")
    # ... rest of your login logic
```

### 5. **Quick Tests**

#### **Test with curl**
```bash
curl -X POST http://127.0.0.1:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"your-password"}' \
  -v
```

#### **Test Field Names**
Try different field combinations:
- `{"username": "...", "password": "..."}`
- `{"email": "...", "password": "..."}`
- `{"login": "...", "password": "..."}`

### 6. **Expected Django Response Format**

Your frontend expects this response format:
```json
{
  "token": "your-jwt-token-here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+1234567890"
  }
}
```

### 7. **Debugging Checklist**

- [ ] Check browser console for detailed request/response logs
- [ ] Verify the exact URL being called
- [ ] Confirm request data format
- [ ] Check if auth headers are being sent (should be false for login)
- [ ] Test with curl to isolate frontend vs backend issues
- [ ] Check Django logs for validation errors
- [ ] Verify CORS configuration
- [ ] Confirm field names match backend expectations

### 8. **Next Steps After Debugging**

Once you see the detailed logs, look for:
1. **Request URL**: Is it exactly `http://127.0.0.1:8000/api/v1/auth/login/`?
2. **Request Data**: What exactly is being sent?
3. **Response Data**: What error details are in the 400 response?
4. **Headers**: Are there any unexpected headers?

Share the console output and we can pinpoint the exact issue!
