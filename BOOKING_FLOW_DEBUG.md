# Booking Flow Debug Guide

## Current Flow Implementation

### 1. **Time Slot Selection (BookingPage.jsx)**
```javascript
const handleTimeSlotSelect = (time, employee) => {
    const bookingData = {
        selectedService,
        selectedEmployee: employee,
        selectedDate,
        selectedTime: time,
        selectedAddOns
    };

    if (!isAuthenticated) {
        // Redirect to login with booking data
        navigate('/login', {
            state: { bookingData }
        });
        return;
    }

    // User is authenticated, go to consent form
    navigate('/consent-form', {
        state: { bookingData }
    });
};
```

### 2. **Login Page (LoginPage.jsx)**
```javascript
// Check if this is a booking flow
const isBookingFlow = location.state?.bookingData;
const bookingData = location.state?.bookingData;

const handlePostLoginBookingFlow = () => {
    // For booking flow, always go to consent form first
    navigate('/consent-form', {
        state: { bookingData }
    });
};
```

### 3. **Consent Form (ConsentFormPage.jsx)**
```javascript
useEffect(() => {
    if (!isAuthenticated) {
        navigate('/login');
        return;
    }

    const data = location.state?.bookingData;
    if (!data) {
        navigate('/');
        return;
    }
    
    setBookingData(data);
}, [location.state, navigate, isAuthenticated]);
```

## Debug Steps

### 1. **Test Time Slot Selection**
- Go to booking page
- Select service, date, and click time slot
- Check browser console for:
  ```
  Time slot selected: { time, employee, selectedService, selectedDate, selectedAddOns }
  User not authenticated, redirecting to login with booking data
  ```

### 2. **Test Login Page**
- Should show "You're Almost Booked!" message
- Should display booking summary
- Check console for:
  ```
  LoginPage - isBookingFlow: true
  LoginPage - bookingData: { selectedService, selectedEmployee, selectedDate, selectedTime, selectedAddOns }
  ```

### 3. **Test Post-Login**
- After successful login, check console for:
  ```
  Login successful - handling booking flow with data: { ... }
  Redirecting to consent form with booking data: { ... }
  ```

### 4. **Test Consent Form**
- Should load with booking details
- Check console for:
  ```
  ConsentFormPage useEffect - isAuthenticated: true
  ConsentFormPage useEffect - location.state: { bookingData: { ... } }
  ConsentFormPage - received booking data: { ... }
  Setting booking data: { ... }
  ```

## Common Issues

### **Blank Page Issues:**
1. **Missing booking data** - Check if `location.state?.bookingData` exists
2. **Authentication redirect loop** - Check if user is properly authenticated
3. **Navigation state loss** - Booking data might be lost during navigation

### **Quick Fixes:**
1. **Always default to consent form** for booking flow ✅
2. **Add proper loading states** ✅
3. **Add debugging logs** ✅
4. **Ensure booking data persistence** through navigation

## Test Sequence

1. **Start at home page** (logged out)
2. **Go to booking page**
3. **Select service** (e.g., "Lash Extensions")
4. **Select date** (today or future date)
5. **Click time slot** → Should redirect to login
6. **Login with credentials** → Should show "You're Almost Booked!"
7. **Complete login** → Should redirect to consent form
8. **Check consent form** → Should show booking details

If any step fails, check the browser console for the debug messages listed above.
