# Authentication Testing Guide

## Overview
This React frontend now includes a complete customer authentication system that integrates with a Django backend API at `http://127.0.0.1:8000/api/v1/`.

## Features Implemented

### 1. Login System
- **Login Page**: `/login` - Clean, responsive login form
- **Email/Phone Support**: Users can login with email or phone number
- **Password Authentication**: Secure password input
- **Remember Me**: Optional checkbox for session persistence
- **Social Login Buttons**: Google, Facebook, Apple (UI ready, integration pending)
- **Forgot Password Link**: Link to password reset (route pending)

### 2. Authentication State Management
- **AuthContext**: Centralized authentication state management
- **Token Management**: JWT token storage and validation
- **User Data Persistence**: User profile data stored locally
- **Auto-logout**: Automatic logout on token expiration
- **Loading States**: Proper loading indicators during auth operations

### 3. Header Integration
- **Login Button**: Visible when user is not authenticated
- **User Dropdown**: Shows user avatar and menu when authenticated
- **Profile Access**: Quick access to user profile
- **Logout Functionality**: Clean logout with state clearing

### 4. Protected Routes
- **RequireAuth Component**: Wrapper for protected routes
- **Automatic Redirects**: Redirects to login with return path
- **Loading States**: Shows spinner while checking authentication

### 5. Profile Management
- **Profile Page**: `/profile` - Protected route showing user information
- **User Data Display**: Shows first name, last name, email, phone
- **Navigation**: Easy return to home and logout options

## Testing the Authentication Flow

### Real Backend Integration
The authentication system now properly integrates with your Django backend at `http://127.0.0.1:8000/api/v1/`.

**Important Fix Applied**: The login endpoint no longer sends authentication headers, preventing the "token expired" error you encountered.

### Testing Steps

1. **Visit the Homepage**
   - Go to `http://localhost:3000`
   - Notice the "Login" button in the top-right corner of the header

2. **Test Login Flow**
   - Click the "Login" button
   - You'll be redirected to `/login`
   - Enter the mock credentials above
   - Click "Sign in"
   - You should see a success toast and be redirected back to the homepage

3. **Test Authenticated State**
   - After login, notice the header now shows a user avatar (initials "TU")
   - Click the avatar to see the dropdown menu
   - The dropdown shows user name, email, and options for "Your Profile" and "Sign out"

4. **Test Profile Page**
   - Click "Your Profile" from the dropdown
   - You'll be taken to `/profile` which shows user information
   - This is a protected route - try accessing it directly when logged out

5. **Test Protected Routes**
   - Log out using the "Sign out" option
   - Try to access `/profile` directly
   - You should be redirected to `/login` with the intended path preserved
   - After logging in, you'll be redirected back to `/profile`

6. **Test Logout**
   - Click "Sign out" from the user dropdown
   - You should be logged out and the header should show the "Login" button again
   - Local storage should be cleared of authentication data

## API Integration

### Backend Requirements
The frontend expects the following API endpoints on your Django backend:

```
POST /api/v1/auth/login/
- Body: { "username": "email_or_phone", "password": "password" }
- Response: { "token": "jwt_token", "user": { user_object } }

GET /api/v1/auth/user/
- Headers: Authorization: Bearer {token}
- Response: { user_object }

POST /api/v1/auth/logout/
- Headers: Authorization: Bearer {token}
- Response: { "message": "Logged out successfully" }
```

### User Object Structure
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890"
}
```

## Visual Design
The authentication system maintains the visual consistency with your existing design:
- Uses the same color scheme (indigo primary colors)
- Responsive design that works on mobile and desktop
- Clean, professional styling matching the booking interface
- Proper loading states and error handling
- Toast notifications for user feedback

## Next Steps
1. **Backend Integration**: Connect to your actual Django API
2. **Registration**: Add user registration functionality
3. **Password Reset**: Implement forgot password flow
4. **Social Login**: Integrate with actual OAuth providers
5. **Profile Editing**: Allow users to update their profile information
6. **Email Verification**: Add email verification for new accounts

## File Structure
```
src/
├── features/auth/
│   ├── AuthContext.jsx          # Authentication context and hooks
│   └── services/
│       └── authApi.js           # API service for authentication
├── pages/Auth/
│   ├── LoginPage.jsx           # Login page component
│   └── ProfilePage.jsx         # User profile page
├── components/
│   ├── RequireAuth.jsx         # Protected route wrapper
│   └── layout/
│       └── Header.jsx          # Updated header with auth state
└── App.jsx                     # Updated with auth routes and provider
```
