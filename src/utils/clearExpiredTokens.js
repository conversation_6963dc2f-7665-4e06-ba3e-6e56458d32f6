// Utility to clear expired tokens on app startup
export const clearExpiredTokens = () => {
  const token = localStorage.getItem('auth_token');
  
  if (token) {
    try {
      // Check if token is expired (basic check for JWT)
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      
      if (payload.exp && payload.exp < currentTime) {
        console.log('Clearing expired token');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
      }
    } catch (error) {
      // If token is malformed, clear it
      console.log('Clearing malformed token');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    }
  }
};
