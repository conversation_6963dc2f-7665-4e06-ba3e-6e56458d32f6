import { useState } from 'react';
import ServicesList from '../../features/booking/components/ServicesList';

function BookingPage() {
  const [selectedService, setSelectedService] = useState(null);
  const [currentStep, setCurrentStep] = useState('services'); // 'services', 'booking', 'confirmation'

  const handleServiceSelect = (service, addOns = []) => {
    console.log('Service selected:', service, addOns);
    setSelectedService(service);
    setCurrentStep('booking');
  };

  const handleBackToServices = () => {
    setCurrentStep('services');
    setSelectedService(null);
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'services':
        return <ServicesList onServiceSelect={handleServiceSelect} />;
      
      case 'booking':
        return (
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto">
              <button 
                onClick={handleBackToServices}
                className="mb-4 text-primary-600 hover:text-primary-700"
              >
                ← Back to Services
              </button>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-4">Book Your Appointment</h2>
                
                {selectedService && (
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-semibold text-lg">{selectedService.name}</h3>
                    <p className="text-gray-600 mb-2">{selectedService.description}</p>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="font-semibold text-primary-600">
                        ${selectedService.price}
                      </span>
                      {selectedService.duration && (
                        <span className="text-gray-500">
                          Duration: {selectedService.duration} mins
                        </span>
                      )}
                    </div>
                  </div>
                )}
                
                <div className="text-center py-12">
                  <p className="text-gray-600 mb-4">
                    Booking form will be implemented here
                  </p>
                  <p className="text-sm text-gray-500">
                    This will include date/time selection, customer information, and payment details
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return <ServicesList onServiceSelect={handleServiceSelect} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {renderCurrentStep()}
    </div>
  );
}

export default BookingPage;
