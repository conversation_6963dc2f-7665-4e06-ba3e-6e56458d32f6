import { useLocation, useNavigate } from 'react-router-dom';

const BookingConfirmationPage = () => {
    const location = useLocation();
    const navigate = useNavigate();
    
    // Get booking data from navigation state
    const bookingData = location.state || {};
    const { selectedService, selectedEmployee, selectedDate, selectedTime, selectedAddOns, bookingId } = bookingData;

    const calculateTotal = () => {
        if (!selectedService) return 0;
        
        const servicePrice = typeof selectedService.price === 'number' 
            ? selectedService.price 
            : parseFloat(selectedService.price || 0);
            
        const addOnsTotal = (selectedAddOns || []).reduce((total, addOn) => {
            const addOnPrice = typeof addOn.price === 'number' 
                ? addOn.price 
                : parseFloat(addOn.price || 0);
            return total + addOnPrice;
        }, 0);
        
        return servicePrice + addOnsTotal;
    };

    if (!selectedService || !bookingId) {
        return (
            <div style={{ padding: '2rem', textAlign: 'center' }}>
                <h2>No booking confirmation found</h2>
                <p>Please start your booking from the beginning.</p>
                <button onClick={() => navigate('/')}>Go Home</button>
            </div>
        );
    }

    return (
        <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto', textAlign: 'center' }}>
            <div style={{ backgroundColor: '#d4edda', color: '#155724', padding: '1rem', borderRadius: '8px', marginBottom: '2rem' }}>
                <h1>✅ Booking Confirmed!</h1>
                <p>Your appointment has been successfully booked.</p>
            </div>
            
            <div style={{ backgroundColor: '#f8f9fa', padding: '1.5rem', borderRadius: '8px', marginBottom: '2rem', textAlign: 'left' }}>
                <h3>Booking Details</h3>
                <p><strong>Booking ID:</strong> {bookingId}</p>
                <p><strong>Service:</strong> {selectedService.name}</p>
                
                {selectedEmployee && (
                    <p><strong>Employee:</strong> {selectedEmployee.display_name || selectedEmployee.name}</p>
                )}
                
                <p><strong>Date:</strong> {selectedDate}</p>
                <p><strong>Time:</strong> {selectedTime}</p>
                
                {selectedAddOns && selectedAddOns.length > 0 && (
                    <div>
                        <h4>Add-ons:</h4>
                        <ul>
                            {selectedAddOns.map(addon => (
                                <li key={addon.id}>
                                    {addon.name} - ${typeof addon.price === 'number' ? addon.price.toFixed(2) : parseFloat(addon.price).toFixed(2)}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
                
                <div style={{ borderTop: '2px solid #9c7b65', paddingTop: '1rem', marginTop: '1rem' }}>
                    <p><strong>Total Paid: ${calculateTotal().toFixed(2)}</strong></p>
                </div>
            </div>
            
            <div style={{ backgroundColor: '#fff3cd', color: '#856404', padding: '1rem', borderRadius: '8px', marginBottom: '2rem' }}>
                <h4>What's Next?</h4>
                <p>You will receive a confirmation email shortly with all the details.</p>
                <p>If you need to make any changes, please call us at <strong>(425) 200-5240</strong></p>
            </div>
            
            <button 
                onClick={() => navigate('/')}
                style={{ 
                    padding: '0.75rem 2rem', 
                    backgroundColor: '#9c7b65', 
                    color: 'white', 
                    border: 'none', 
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '1rem'
                }}
            >
                Book Another Appointment
            </button>
        </div>
    );
};

export default BookingConfirmationPage;
