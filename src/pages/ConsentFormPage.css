/* Consent Form Page */
.consent-form-page {
    min-height: 100vh;
    background-color: #ffffff;
    padding: 20px 0;
}

.consent-form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.consent-form-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 0;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Main Content Area */
.consent-form-main {
    padding: 40px;
    background-color: #ffffff;
}

.business-header {
    margin-bottom: 40px;
}

.business-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Consent Form Section */
.consent-form-section {
    margin-bottom: 40px;
}

.consent-form-section h2 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0 0 15px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.consent-divider {
    width: 60px;
    height: 3px;
    background-color: #333;
    margin-bottom: 30px;
}

/* Consent Text */
.consent-text {
    margin-bottom: 40px;
    line-height: 1.6;
}

.consent-text p {
    font-size: 14px;
    color: #555;
    margin-bottom: 20px;
    text-align: justify;
}

.consent-text h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 25px 0 15px 0;
}

/* Signature Section */
.signature-section {
    margin-bottom: 40px;
}

.signature-checkbox {
    margin-bottom: 20px;
}

.signature-checkbox label {
    display: block;
    margin-bottom: 15px;
}

.required-asterisk {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.required-asterisk::after {
    content: " *";
    color: #dc3545;
}

.electronic-records-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
}

.electronic-records-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
}

.electronic-records-checkbox label {
    font-size: 14px;
    color: #555;
    margin: 0;
    cursor: pointer;
}

.link-text {
    color: #007bff;
    text-decoration: none;
}

.link-text:hover {
    text-decoration: underline;
}

/* Signature Pad */
.signature-pad {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    padding: 15px;
}

.signature-input {
    width: 100%;
    height: 120px;
    border: none;
    background: transparent;
    resize: none;
    font-family: cursive;
    font-size: 16px;
    color: #333;
    outline: none;
    padding: 10px 0;
}

.signature-input::placeholder {
    color: #999;
    font-style: italic;
}

.clear-signature-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    background-color: #fff;
}

.clear-signature-btn:hover {
    background-color: #f5f5f5;
    border-color: #999;
}

.signature-timestamp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
    font-size: 12px;
    color: #666;
}

.signature-label {
    font-weight: 500;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 40px;
}

.back-btn,
.next-btn {
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.back-btn {
    background-color: #6c757d;
    color: white;
}

.back-btn:hover:not(:disabled) {
    background-color: #5a6268;
}

.next-btn {
    background-color: #28a745;
    color: white;
}

.next-btn:hover:not(:disabled) {
    background-color: #218838;
}

.back-btn:disabled,
.next-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Mobile Order Summary */
.mobile-order-summary {
    display: none;
}

/* Mobile Back Arrow */
.mobile-back-arrow {
    display: none;
}

.mobile-back-btn {
    background: none;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    color: #333;
    display: flex;
    align-items: center;
}

.mobile-back-btn:hover {
    background-color: #f5f5f5;
}

.mobile-back-btn svg {
    width: 24px;
    height: 24px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .consent-form-page {
        padding: 0;
        min-height: 100vh;
    }

    .consent-form-container {
        padding: 0;
        max-width: none;
    }

    .consent-form-content {
        grid-template-columns: 1fr;
        gap: 0;
        box-shadow: none;
        border-radius: 0;
    }

    .consent-form-main {
        padding: 20px;
    }

    .business-header h1 {
        font-size: 24px;
    }

    .consent-form-section h2 {
        font-size: 20px;
    }

    .consent-text p {
        font-size: 13px;
    }

    .form-actions {
        flex-direction: column;
    }

    .back-btn,
    .next-btn {
        width: 100%;
        padding: 15px;
    }

    /* Show mobile components and hide desktop sidebar */
    .mobile-back-arrow {
        display: block;
    }

    .mobile-order-summary {
        display: block;
    }

    .appointment-summary-sidebar {
        display: none;
    }
}
