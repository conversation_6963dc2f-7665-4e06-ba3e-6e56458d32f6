import { useState, useEffect } from 'react';
import useApi from '../hooks/useApi';
import publicApi from '../utils/publicApi';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../features/auth/AuthContext';
import { useBooking } from '../features/booking/BookingContext';
import './ConsentFormPage.css';
import SignaturePad from '../components/SignaturePad';

const ConsentFormPage = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { user, isAuthenticated } = useAuth();
    const {
        bookingData,
        setConsentData,
        canProceedToConsent,
        canProceedToReview
    } = useBooking();
    const [signature, setSignature] = useState('');
    const [agreeToElectronicRecords, setAgreeToElectronicRecords] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isOrderSummaryExpanded, setIsOrderSummaryExpanded] = useState(false);

    /* ------------------------------------------------------------------
       Dynamic form template + answers state
    ------------------------------------------------------------------ */
    // useApi attaches Authorization header – for public template listing we need *no* auth header.
    // So we directly use publicApi here.
    const fetchTemplates = async (params = {}) => {
        const res = await publicApi.get('/forms/templates/', { params });
        return res.data;
    };
    const [template, setTemplate] = useState(null);
    const [answers, setAnswers] = useState({});

    const setAnswer = (qid, value) => {
        setAnswers(prev => ({ ...prev, [qid]: value }));
    };

    // Load the first published template for the current business (or id=1 as fallback)
    useEffect(() => {
        const loadTemplate = async () => {
            try {
                const res = await fetchTemplates({
                    business_id: bookingData?.businessId || 1,
                    status: 'Published',
                });
                if (res?.results?.length) {
                    setTemplate(res.results[0]);
                }
            } catch (err) {
                console.error('Unable to load consent form template', err);
            }
        };
        if (isAuthenticated) {
            loadTemplate();
        }
    }, [isAuthenticated, bookingData?.businessId]);

    useEffect(() => {
        console.log('ConsentFormPage useEffect - isAuthenticated:', isAuthenticated);
        console.log('ConsentFormPage useEffect - bookingData:', bookingData);

        // Check authentication first
        if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            navigate('/login');
            return;
        }

        // Check if we have the required booking data to proceed to consent
        if (!canProceedToConsent()) {
            console.log('Missing required booking data, redirecting to booking page');
            navigate('/');
            return;
        }

        console.log('ConsentFormPage - booking data available:', bookingData);
    }, [isAuthenticated, navigate, canProceedToConsent, bookingData]);

    const handleBack = () => {
        // Go back to the booking calendar page
        navigate('/');
    };

    const handleNext = async () => {
        if (!agreeToElectronicRecords || !signature.trim()) {
            alert('Please complete the signature and agree to electronic records.');
            return;
        }

        setIsLoading(true);

        try {
            // Update booking context with consent data
            setConsentData({
                signature: signature,
                consentAgreed: true
            });

            // Store consent status for this user
            if (user?.id) {
                localStorage.setItem(`consent_signed_${user.id}`, 'true');
            }

            // Check if we can proceed to review
            if (canProceedToReview()) {
                navigate('/review-booking');
            } else {
                console.error('Cannot proceed to review - missing required data');
                alert('Missing required booking information. Please start over.');
                navigate('/');
            }
        } catch (error) {
            console.error('Error processing consent:', error);
            alert('An error occurred. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const clearSignature = () => {
        setSignature('');
    };

    const getCurrentDateTime = () => {
        const now = new Date();
        return now.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        }) + ' - ' + now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    // If we don't have required booking data, the useEffect will redirect
    if (!canProceedToConsent()) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading booking details...</p>
                </div>
            </div>
        );
    }

    const totalCost = parseFloat(bookingData.selectedService?.price || 0) +
                     (bookingData.selectedAddOns?.reduce((sum, addon) => sum + parseFloat(addon.price || 0), 0) || 0);

    // Debug employee data
    console.log('ConsentFormPage - Employee data:', bookingData.selectedEmployee);

    /* ------------------------------------------------------------------
       Renderer for each question type coming from template.content.questions
    ------------------------------------------------------------------ */
    const renderQuestion = (q) => {
        switch (q.type) {
            case 'text': {
                // Support the textStyle prop coming from builder: Heading, Subheading, Paragraph
                const style = q.textStyle || 'Paragraph';
                if (style === 'Heading') {
                    return (
                        <h2 key={q.id} className="text-xl lg:text-2xl font-bold text-gray-900 mb-4 whitespace-pre-wrap">
                            {q.textContent || q.label}
                        </h2>
                    );
                }
                if (style === 'Subheading') {
                    return (
                        <h3 key={q.id} className="text-lg font-semibold text-gray-900 mb-2 whitespace-pre-wrap">
                            {q.textContent || q.label}
                        </h3>
                    );
                }
                // Default paragraph
                return (
                    <p key={q.id} className="mb-4 text-gray-700 whitespace-pre-wrap">
                        {q.textContent || q.label}
                    </p>
                );
            }
            case 'heading':
            case 'subheading':
                return (
                    <h3 key={q.id} className="font-bold text-gray-900 mt-6 mb-2">
                        {q.textContent || q.label}
                    </h3>
                );
            case 'short':
                return (
                    <div key={q.id} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">{q.label}</label>
                        <input
                            className="w-full border rounded px-3 py-2"
                            value={answers[q.id] || ''}
                            onChange={(e) => setAnswer(q.id, e.target.value)}
                        />
                    </div>
                );
            case 'long':
                return (
                    <div key={q.id} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">{q.label}</label>
                        <textarea
                            className="w-full border rounded px-3 py-2"
                            rows={4}
                            value={answers[q.id] || ''}
                            onChange={(e) => setAnswer(q.id, e.target.value)}
                        />
                    </div>
                );
            case 'multiple':
                return (
                    <div key={q.id} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">{q.label}</label>
                        {q.options?.map((opt, idx) => (
                            <div key={idx} className="flex items-center mb-1">
                                <input
                                    type="radio"
                                    id={`${q.id}-${idx}`}
                                    name={`q-${q.id}`}
                                    value={opt}
                                    checked={answers[q.id] === opt}
                                    onChange={() => setAnswer(q.id, opt)}
                                    className="mr-2"
                                />
                                <label htmlFor={`${q.id}-${idx}`} className="text-sm text-gray-700">{opt}</label>
                            </div>
                        ))}
                    </div>
                );
            case 'signature':
                return (
                    <div key={q.id} className="signature-section mt-8">
                        <div className="signature-checkbox mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <span className="text-red-500">Please sign here *</span>
                            </label>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id={`electronic-records-${q.id}`}
                                    checked={agreeToElectronicRecords}
                                    onChange={(e) => setAgreeToElectronicRecords(e.target.checked)}
                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                />
                                <label htmlFor={`electronic-records-${q.id}`} className="text-sm text-gray-700">
                                    I agree to use <span className="text-blue-600 underline">electronic records and signatures.</span>
                                </label>
                            </div>
                        </div>

                        <SignaturePad
                            value={signature}
                            onChange={(dataUrl) => {
                                setSignature(dataUrl);
                                setAnswer(q.id, dataUrl);
                            }}
                        />
                        <div className="flex justify-end mt-2 text-sm text-gray-600">
                            <span>{getCurrentDateTime()}</span>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen bg-white">
            {/* Simple Top Header */}
            <div className="bg-white">
                <div className="max-w-6xl mx-auto border-b border-gray-200 px-6 py-3">
                    <div className="flex justify-end items-center">
                        <div className="flex items-center space-x-3">
                            <span className="text-sm text-gray-700">Welcome, DylanStylistTest Z</span>
                            <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                DZ
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile Order Summary - Collapsible */}
            <div className="lg:hidden bg-white border-b border-gray-200">
                <div className="max-w-6xl mx-auto px-6">
                    <button
                        onClick={() => setIsOrderSummaryExpanded(!isOrderSummaryExpanded)}
                        className="w-full py-4 flex justify-between items-center text-left"
                    >
                        <h3 className="text-lg font-semibold text-gray-900">Order Summary</h3>
                        <span className="text-gray-500 text-xl">
                            {isOrderSummaryExpanded ? '−' : '+'}
                        </span>
                    </button>

                    {isOrderSummaryExpanded && (
                        <div className="pb-6 border-t border-gray-200">
                            <div className="pt-4">
                                {/* Date and Time */}
                                <div className="mb-4">
                                    <div className="text-sm text-gray-900 font-medium">
                                        {bookingData.selectedDate ?
                                            new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
                                                weekday: 'short',
                                                month: 'short',
                                                day: 'numeric',
                                                year: 'numeric'
                                            }) : 'Date not selected'
                                        } {bookingData.selectedTime ?
                                            new Date(`2000-01-01 ${bookingData.selectedTime}`).toLocaleTimeString('en-US', {
                                                hour: 'numeric',
                                                minute: '2-digit',
                                                hour12: true
                                            }) : 'Time not selected'
                                        }
                                    </div>
                                </div>

                                {/* Stylist Info */}
                                <div className="mb-4 pb-4 border-b border-gray-300">
                                    <div className="flex items-start space-x-3">
                                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                            <span className="text-sm font-medium text-gray-700">
                                                {(bookingData.selectedEmployee?.display_name || bookingData.selectedEmployee?.name || bookingData.selectedEmployee?.full_name)?.charAt(0) || 'S'}
                                            </span>
                                        </div>
                                        <div className="flex-1">
                                            <div className="text-sm text-gray-600">
                                                {bookingData.selectedEmployee?.title || bookingData.selectedEmployee?.role || 'Stylist'}
                                            </div>
                                            <div className="font-medium text-gray-900">
                                                {bookingData.selectedEmployee?.display_name ||
                                                 bookingData.selectedEmployee?.name ||
                                                 bookingData.selectedEmployee?.full_name ||
                                                 'Stylist'}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Services and Total */}
                                <div className="mb-4">
                                    {/* Main Service */}
                                    <div className="flex justify-between items-center mb-2">
                                        <div className="text-sm text-gray-900">
                                            {bookingData.selectedService?.name || 'Classic Lash Fullset(C01-C02)'}
                                        </div>
                                        <div className="font-medium text-gray-900">
                                            ${bookingData.selectedService?.price || '199.00'}
                                        </div>
                                    </div>

                                    {/* Add-ons */}
                                    {bookingData.selectedAddOns && bookingData.selectedAddOns.length > 0 && (
                                        bookingData.selectedAddOns.map((addon, index) => (
                                            <div key={index} className="flex justify-between items-center mb-2">
                                                <div className="text-sm text-gray-900">
                                                    {addon.name}
                                                </div>
                                                <div className="font-medium text-gray-900">
                                                    ${addon.price}
                                                </div>
                                            </div>
                                        ))
                                    )}

                                    {/* Total */}
                                    <div className="flex justify-between items-center text-lg font-semibold mt-4">
                                        <span>Total</span>
                                        <span>${totalCost.toFixed(2)}</span>
                                    </div>
                                </div>

                                {/* Payment Summary */}
                                <div className="pt-4 border-t border-gray-300">
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="text-sm text-gray-900">Total Due Now</span>
                                        <span className="font-medium text-gray-900">$0.00</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-gray-900">Total Due at Business</span>
                                        <span className="font-medium text-gray-900">${totalCost.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content - Centered with Padding */}
            <div className="max-w-6xl mx-auto">
                <div className="flex">
                    {/* Main Content */}
                    <div className="flex-1 bg-white p-4 lg:p-8">
                            <div className="business-header">
                                <h1 className="text-2xl font-bold text-gray-900 mb-6">Clément Lash</h1>
                            </div>

                            <div className="consent-form-section">
                                {!template ? (
                                    <p className="text-gray-600">Loading form...</p>
                                ) : (
                                    template.content?.questions?.map(renderQuestion)
                                )}
                            </div>

                            <div className="form-actions mt-8 flex justify-between">
                                <button
                                    type="button"
                                    className="px-6 py-2 text-gray-600 hover:text-gray-900 transition-colors"
                                    onClick={handleBack}
                                    disabled={isLoading}
                                >
                                    ← Back
                                </button>
                                <button
                                    type="button"
                                    className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    onClick={handleNext}
                                    disabled={isLoading || !agreeToElectronicRecords || !signature.trim()}
                                >
                                    {isLoading ? 'Processing...' : 'Next'}
                                </button>
                            </div>
                        </div>

                {/* Order Summary Sidebar - Desktop Only */}
                <div className="hidden lg:block w-80 bg-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

                    {/* Date and Time */}
                    <div className="mb-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedDate ?
                                        new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
                                            weekday: 'short',
                                            month: 'short',
                                            day: 'numeric',
                                            year: 'numeric'
                                        }) : 'Date not selected'
                                    }
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedTime ?
                                        new Date(`2000-01-01 ${bookingData.selectedTime}`).toLocaleTimeString('en-US', {
                                            hour: 'numeric',
                                            minute: '2-digit',
                                            hour12: true
                                        }) : 'Time not selected'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Stylist Info */}
                    <div className="mb-6 pb-4 border-b border-gray-300">
                        <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-sm font-medium text-gray-700">
                                    {(bookingData.selectedEmployee?.display_name || bookingData.selectedEmployee?.name || bookingData.selectedEmployee?.full_name)?.charAt(0) || 'S'}
                                </span>
                            </div>
                            <div className="flex-1">
                                <div className="text-sm text-gray-600">
                                    {bookingData.selectedEmployee?.title || bookingData.selectedEmployee?.role || 'Stylist'}
                                </div>
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedEmployee?.display_name ||
                                     bookingData.selectedEmployee?.name ||
                                     bookingData.selectedEmployee?.full_name ||
                                     'Stylist'}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Services and Total */}
                    <div className="mb-6">
                        {/* Main Service */}
                        <div className="flex justify-between items-center mb-2">
                            <div className="text-sm text-gray-900">
                                {bookingData.selectedService?.name || 'Classic Lash Fullset(C01-C02)'}
                            </div>
                            <div className="font-medium text-gray-900">
                                ${bookingData.selectedService?.price || '199.00'}
                            </div>
                        </div>

                        {/* Add-ons */}
                        {bookingData.selectedAddOns && bookingData.selectedAddOns.length > 0 && (
                            bookingData.selectedAddOns.map((addon, index) => (
                                <div key={index} className="flex justify-between items-center mb-2">
                                    <div className="text-sm text-gray-900">
                                        {addon.name}
                                    </div>
                                    <div className="font-medium text-gray-900">
                                        ${addon.price}
                                    </div>
                                </div>
                            ))
                        )}

                        {/* Total */}
                        <div className="flex justify-between items-center text-lg font-semibold mt-4">
                            <span>Total</span>
                            <span>${totalCost.toFixed(2)}</span>
                        </div>
                    </div>

                    {/* Payment Summary */}
                    <div className="mb-6 pb-4 border-b border-gray-300 pt-4 border-t border-gray-300">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-900">Total Due Now</span>
                            <span className="font-medium text-gray-900">$0.00</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-900">Total Due at Business</span>
                            <span className="font-medium text-gray-900">${totalCost.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    );
};

export default ConsentFormPage;
