/**
 * Booking API Service
 * This file contains all API calls related to the booking functionality
 * It interfaces with the Django backend APIs
 */

// Base API URL - would come from env config in a real app
const API_BASE_URL = 'http://localhost:8000/api/v1';

// API endpoints based on Django backend structure from api/urls.py
const ENDPOINTS = {
    services: '/services/',
    serviceCategories: '/service-categories/',
    employees: '/employees/',
    businessCustomers: '/business-customers/',
    availableTimes: '/businesses/{businessId}/appointments/available-times/',
    booking: '/appointments/',
    businessHours: '/business/hours/'
};

// Helper function for handling API requests
const fetchAPI = async (url, options = {}) => {
    try {
        // Create a new options object with default headers
        const requestOptions = { 
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };
        
        // We don't need to add authorization for the public endpoints
        // The PublicReadOnlyPrivateWrite permission class will handle this

        const response = await fetch(url, requestOptions);
        
        // Log the response for debugging
        console.log(`API call to ${url}:`, response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API error response:', errorText);
            
            try {
                // Try to parse as JSON
                const errorData = JSON.parse(errorText);
                throw new Error(errorData.detail || errorData.error || 'API request failed');
            } catch (e) {
                // If not JSON, use text
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Error in API call to ${url}:`, error);
        throw error;
    }
};

/**
 * Handle paginated responses by extracting the results array
 * @param {Object} data - The response data from the API
 * @returns {Array} The results array or the original data if not paginated
 */
const handlePaginatedResponse = (data) => {
    // Check if the response is paginated (has results property)
    if (data && data.results && Array.isArray(data.results)) {
        console.log('Received paginated response with', data.results.length, 'items');
        return data.results;
    }
    
    // If not paginated or unexpected format, return original data
    return data;
};

/**
 * Fetch all available services
 * @returns {Promise} Promise resolving to array of services
 */
export const getServices = async () => {
    let allServices = [];
    let nextUrl = `${API_BASE_URL}${ENDPOINTS.services}`;
    
    while (nextUrl) {
        try {
            console.log('Fetching services from:', nextUrl);
            const data = await fetchAPI(nextUrl);
            
            // Add the current page results to our collection
            if (data && data.results && Array.isArray(data.results)) {
                allServices = [...allServices, ...data.results];
            }
            
            // Update the next URL for pagination
            nextUrl = data.next;
        } catch (error) {
            console.error('Error fetching services page:', error);
            break;
        }
    }
    
    console.log(`Fetched a total of ${allServices.length} services`);
    return allServices;
};

/**
 * Fetch all service categories
 * @returns {Promise} Promise resolving to array of service categories
 */
export const getServiceCategories = async () => {
    let allCategories = [];
    let nextUrl = `${API_BASE_URL}${ENDPOINTS.serviceCategories}`;
    
    while (nextUrl) {
        try {
            const data = await fetchAPI(nextUrl);
            
            // Add the current page results to our collection
            if (data && data.results && Array.isArray(data.results)) {
                allCategories = [...allCategories, ...data.results];
            }
            
            // Update the next URL for pagination
            nextUrl = data.next;
        } catch (error) {
            console.error('Error fetching category page:', error);
            break;
        }
    }
    
    console.log(`Fetched a total of ${allCategories.length} service categories`);
    return allCategories;
};

/**
 * Fetch all employees
 * @returns {Promise} Promise resolving to array of employees
 */
export const getEmployees = async () => {
    let allEmployees = [];
    let nextUrl = `${API_BASE_URL}${ENDPOINTS.employees}`;
    
    while (nextUrl) {
        try {
            console.log('Fetching employees from:', nextUrl);
            const data = await fetchAPI(nextUrl);
            
            // Add the current page results to our collection
            if (data && data.results && Array.isArray(data.results)) {
                allEmployees = [...allEmployees, ...data.results];
            }
            
            // Update the next URL for pagination
            nextUrl = data.next;
        } catch (error) {
            console.error('Error fetching employees page:', error);
            break;
        }
    }
    
    console.log(`Fetched a total of ${allEmployees.length} employees`);
    return allEmployees;
};

/**
 * Unified method to fetch available time slots
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {number} serviceId - ID of the selected service
 * @param {number} businessId - ID of the business
 * @param {number|null} employeeId - ID of specific employee, or null for all employees
 * @returns {Promise} Promise resolving to standardized time slots format
 */
export const getAvailableTimeSlots = async (date, serviceId, businessId, employeeId = null) => {
    let url = `${API_BASE_URL}/businesses/${businessId}/appointments/available-times/?date=${date}&service_id=${serviceId}`;

    if (employeeId) {
        url += `&employee_id=${employeeId}`;
    }

    try {
        const response = await fetchAPI(url);
        console.log('Raw API response:', response);

        // Standardize the response format regardless of whether it's for one employee or all
        return standardizeTimeSlotsResponse(response, employeeId);
    } catch (error) {
        console.error('Error fetching time slots:', error);
        throw error;
    }
};

/**
 * Standardize time slots response to consistent format
 * @param {*} response - Raw API response
 * @param {number|null} employeeId - Employee ID if specific employee requested
 * @returns {Object} Standardized format: { employeeName: [slots] }
 */
const standardizeTimeSlotsResponse = (response, employeeId) => {
    // Handle different API response formats and normalize to: { employeeName: [slots] }

    if (!response) {
        return {};
    }

    // Format 1: New API with 'availability' object { availability: { "Employee Name": ["2024-01-01T10:00:00Z", ...] } }
    if (response.availability && typeof response.availability === 'object') {
        const standardized = {};

        Object.keys(response.availability).forEach(employeeName => {
            const timeSlots = response.availability[employeeName];
            standardized[employeeName] = timeSlots.map(isoDateTime => {
                const date = new Date(isoDateTime);
                const timeString = date.toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'America/Los_Angeles'
                });
                return {
                    time: timeString,
                    available: true,
                    datetime: isoDateTime
                };
            });
        });

        return standardized;
    }

    // Format 2: Array of employee objects [{ employee: {...}, slots: [...] }]
    if (Array.isArray(response) && response.length > 0 && response[0].employee) {
        const standardized = {};

        response.forEach(empData => {
            const employeeName = empData.employee.display_name || empData.employee.full_name || `Employee #${empData.employee.id}`;
            standardized[employeeName] = empData.slots || [];
        });

        return standardized;
    }

    // Format 3: Simple array of time slots (for specific employee)
    if (Array.isArray(response)) {
        // Need employee name - this should be provided by the calling code
        const employeeName = `Employee #${employeeId || 'Unknown'}`;

        const processedSlots = response.map(slot => {
            if (typeof slot === 'string') {
                // ISO datetime string
                if (slot.includes('T')) {
                    const date = new Date(slot);
                    const timeString = date.toLocaleTimeString('en-US', {
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit',
                        timeZone: 'America/Los_Angeles'
                    });
                    return {
                        time: timeString,
                        available: true,
                        datetime: slot
                    };
                } else {
                    // Simple time string
                    return {
                        time: slot,
                        available: true
                    };
                }
            } else if (slot.time) {
                // Already formatted slot object
                return slot;
            }
            return slot;
        });

        return { [employeeName]: processedSlots };
    }

    // Format 4: Object with employee names as keys (legacy)
    if (typeof response === 'object' && !Array.isArray(response)) {
        return response;
    }

    console.warn('Unknown time slots response format:', response);
    return {};
};

/**
 * Create a new booking
 * @param {Object} bookingData - Data for the new booking
 * @returns {Promise} Promise resolving to the created booking
 */
export const createBooking = async (bookingData) => {
    try {
        // Format the data for the API
        const formattedData = {
            service_id: bookingData.service,
            employee_id: bookingData.employee,
            date: bookingData.date,
            time: bookingData.time,
            add_on_ids: bookingData.addOns || [],
            customer: {
                name: bookingData.customer.name,
                email: bookingData.customer.email,
                phone: bookingData.customer.phone,
                address: bookingData.customer.address,
                address2: bookingData.customer.address2 || ''
            },
            notes: bookingData.notes || '',
            payment_method: bookingData.payment?.holdWithCard ? 'card_hold' : 'at_appointment'
        };

        console.log('Sending booking data to API:', formattedData);

        const url = `${API_BASE_URL}${ENDPOINTS.booking}`;
        return await fetchAPI(url, {
            method: 'POST',
            body: JSON.stringify(formattedData)
        });
    } catch (error) {
        console.error('Error creating booking:', error);
        throw error;
    }
};

/**
 * Get business operating hours
 * @returns {Promise} Promise resolving to business hours
 */
export const getBusinessHours = async () => {
    return fetchAPI(`${API_BASE_URL}${ENDPOINTS.businessHours}`);
};

/**
 * Validates availability before booking
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} time - Time in HH:MM format
 * @param {number} serviceId - ID of the selected service
 * @param {number} employeeId - ID of the selected employee (optional)
 * @returns {Promise} Promise resolving to boolean indicating availability
 */
export const checkAvailability = async (date, time, serviceId, employeeId) => {
    try {
        const timeSlots = await getAvailableTimeSlots(date, serviceId, employeeId);
        return timeSlots.some(slot => slot.time === time && slot.available);
    } catch (error) {
        console.error('Error checking availability:', error);
        throw error;
    }
};

/**
 * Get booking rules for a business
 * @param {number} businessId - Business ID
 * @returns {Promise} Promise resolving to booking rules
 */
export const getBookingRules = async (businessId) => {
    return fetchAPI(`${API_BASE_URL}/booking-rules/by-business/${businessId}/`);
};
