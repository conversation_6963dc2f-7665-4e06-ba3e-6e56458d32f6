import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './features/auth/AuthContext';
import { BookingProvider } from './features/booking/BookingContext';
import Layout from './components/layout/Layout.jsx';
import MainContentWrapper from './components/MainContentWrapper.jsx';
import ConsentFormPage from './pages/ConsentFormPage.jsx';
import ReviewBookingPage from './pages/ReviewBookingPage.jsx';
import BookingConfirmationPage from './pages/BookingConfirmationPage.jsx';
import LoginPage from './pages/Auth/LoginPage.jsx';
import ProfilePage from './pages/Auth/ProfilePage.jsx';
import AppointmentsPage from './pages/AppointmentsPage.jsx';
import RequireAuth from './components/RequireAuth.jsx';

import { getServices } from './api/bookingApi';
import './App.css';

const App = () => {
    const [selectedService, setSelectedService] = useState(null);
    const [services, setServices] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedAddOns, setSelectedAddOns] = useState([]);
    const [activeTab, setActiveTab] = useState('services'); // 'services' or 'booking'

    // Fetch services on component mount
    useEffect(() => {
        const fetchServices = async () => {
            try {
                setLoading(true);
                const data = await getServices();
                console.log('Services data from API in App.js:', data);
                setServices(data);
            } catch (err) {
                console.error('Error fetching services in App.js:', err);
                // Fallback to static data handled in ServicesList component
                setServices([]);
            } finally {
                setLoading(false);
            }
        };

        fetchServices();
    }, []);

    const businessInfo = {
        name: 'Clément Lash',
        location: 'Bellevue, WA',
        logo: '/images/logo.png'
    };

    const handleTabChange = (tab) => {
        setActiveTab(tab);
    };

    return (
        <AuthProvider>
            <BookingProvider>
                <Router>
                <Routes>
                    <Route
                        path="/"
                        element={
                            <Layout
                                businessInfo={businessInfo}
                                activeTab={activeTab}
                                onTabChange={handleTabChange}
                            >
                                <div className="app-container">
                                    <MainContentWrapper
                                        services={services}
                                        loading={loading}
                                        activeTab={activeTab}
                                        setActiveTab={setActiveTab}
                                        selectedService={selectedService}
                                        setSelectedService={setSelectedService}
                                        selectedAddOns={selectedAddOns}
                                        setSelectedAddOns={setSelectedAddOns}
                                    />
                                </div>
                            </Layout>
                        }
                    />
                    <Route
                        path="/login"
                        element={<LoginPage />}
                    />
                    <Route
                        path="/profile"
                        element={
                            <RequireAuth>
                                <ProfilePage />
                            </RequireAuth>
                        }
                    />
                    <Route
                        path="/appointments"
                        element={
                            <RequireAuth>
                                <AppointmentsPage />
                            </RequireAuth>
                        }
                    />
                    <Route
                        path="/consent-form"
                        element={<ConsentFormPage />}
                    />
                    <Route
                        path="/review-booking"
                        element={<ReviewBookingPage />}
                    />
                    <Route
                        path="/booking-confirmation"
                        element={<BookingConfirmationPage />}
                    />
                </Routes>
                <Toaster
                    position="top-right"
                    toastOptions={{
                        duration: 4000,
                        style: {
                            background: '#363636',
                            color: '#fff',
                        },
                    }}
                />

                </Router>
            </BookingProvider>
        </AuthProvider>
    );
};

export default App;
