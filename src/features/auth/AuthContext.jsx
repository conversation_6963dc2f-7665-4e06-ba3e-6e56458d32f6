import { createContext, useContext, useState, useEffect } from 'react'
import { authService } from './services/authApi'
import { clearExpiredTokens } from '../../utils/clearExpiredTokens'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    // Check if user is logged in on initial load
    const initializeAuth = async () => {
      try {
        // Clear any expired tokens first
        clearExpiredTokens()

        const token = authService.getStoredToken()
        const storedUser = authService.getStoredUser()

        if (token && storedUser) {
          console.log('🔄 Found stored auth data, attempting to restore session');
          console.log('Token:', token ? 'Present' : 'Missing');
          console.log('Stored user:', storedUser);

          // For now, trust the stored user data without API validation
          // This prevents logout on page refresh when backend is unavailable
          setUser(storedUser);

          // Optionally validate token in background (don't clear on failure)
          try {
            const currentUser = await authService.getCurrentUser();
            console.log('✅ Token validation successful, updating user data');
            setUser(currentUser);
          } catch (error) {
            console.warn('⚠️ Token validation failed, but keeping stored user data:', error.message);
            // Don't clear the user - keep them logged in with stored data
            // Only clear if it's a definitive 401/403 error
            if (error.response?.status === 401 || error.response?.status === 403) {
              console.log('🔒 Definitive auth error, clearing session');
              await authService.logout();
              setUser(null);
            }
          }
        } else {
          console.log('🔍 No stored auth data found');
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (credentials) => {
    try {
      setLoading(true)
      setError(null)
      const { token, user: userData } = await authService.login(credentials)
      setUser(userData)
      return { success: true, user: userData }
    } catch (error) {
      console.error('Login error:', error)
      setError(error.message)
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      await authService.logout()
      setUser(null)
      setError(null)
    } catch (error) {
      console.error('Logout error:', error)
      // Still clear user state even if API call fails
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData) => {
    try {
      setLoading(true)
      setError(null)
      const { token, user: newUser } = await authService.register(userData)
      setUser(newUser)
      return { success: true, user: newUser }
    } catch (error) {
      console.error('Registration error:', error)
      setError(error.message)
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const socialLogin = async (provider, token) => {
    try {
      setLoading(true)
      setError(null)
      const { token: authToken, user: userData } = await authService.socialLogin(provider, token)
      setUser(userData)
      return { success: true, user: userData }
    } catch (error) {
      console.error('Social login error:', error)
      setError(error.message)
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const clearError = () => {
    setError(null)
  }

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      error,
      login,
      logout,
      register,
      socialLogin,
      clearError,
      isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
