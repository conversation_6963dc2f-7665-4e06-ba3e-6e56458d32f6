import axios from 'axios';

const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';

// Create axios instance for auth API calls
const authApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// List of public endpoints that don't need authentication
const PUBLIC_ENDPOINTS = [
  '/auth/login/',
  '/auth/register/',
  '/auth/password-reset/',
  '/auth/social/google/',
  '/auth/social/facebook/',
  '/auth/social/apple/'
];

// Request interceptor to add auth token to requests (except public endpoints)
authApi.interceptors.request.use(
  (config) => {
    // Check if this is a public endpoint
    const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint =>
      config.url && config.url.includes(endpoint)
    );

    console.log('📤 API Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      isPublicEndpoint,
      hasAuthHeader: !!config.headers.Authorization,
      data: config.data,
      headers: config.headers
    });

    // Only add auth token for non-public endpoints
    if (!isPublicEndpoint) {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('🔑 Added auth token to request');
      }
    } else {
      console.log('🌐 Public endpoint - no auth token added');
    }

    return config;
  },
  (error) => {
    console.error('📤 Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
authApi.interceptors.response.use(
  (response) => {
    console.log('📥 API Response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.config?.url,
      method: response.config?.method?.toUpperCase(),
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('📥 API Error Response:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      data: error.response?.data,
      headers: error.response?.headers
    });

    // Only handle 401 errors for protected endpoints (not login/register)
    if (error.response?.status === 401) {
      const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint =>
        error.config?.url && error.config.url.includes(endpoint)
      );

      // Only clear tokens and redirect for protected endpoints
      if (!isPublicEndpoint) {
        console.log('🔒 401 on protected endpoint - clearing tokens and redirecting');
        // Token expired or invalid, clear local storage
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        // Redirect to login page
        window.location.href = '/login';
      } else {
        console.log('🌐 401 on public endpoint - not clearing tokens');
      }
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authService = {
  // Login with email/phone and password
  async login(credentials) {
    try {
      console.log('🔐 Login attempt with credentials:', {
        email: credentials.email,
        username: credentials.username, // Keep both for debugging
        password: credentials.password ? '[REDACTED]' : 'undefined',
        hasPassword: !!credentials.password
      });

      const response = await authApi.post('/auth/login/', credentials);
      console.log('✅ Login successful:', {
        status: response.status,
        hasToken: !!response.data?.token,
        hasUser: !!response.data?.user
      });

      const { token, user } = response.data;

      // Store token and user data
      localStorage.setItem('auth_token', token);
      localStorage.setItem('user_data', JSON.stringify(user));

      return { token, user };
    } catch (error) {
      console.error('❌ Login error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          data: error.config?.data,
          headers: error.config?.headers
        }
      });

      // Try to extract meaningful error message
      let errorMessage = 'Login failed';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else {
          errorMessage = JSON.stringify(error.response.data);
        }
      }

      throw new Error(errorMessage);
    }
  },

  // Logout
  async logout() {
    try {
      await authApi.post('/auth/logout/');
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    }
  },

  // Register new user
  async register(userData) {
    try {
      const response = await authApi.post('/auth/register/', userData);
      const { token, user } = response.data;
      
      // Store token and user data
      localStorage.setItem('auth_token', token);
      localStorage.setItem('user_data', JSON.stringify(user));
      
      return { token, user };
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  },

  // Get current user profile
  async getCurrentUser() {
    try {
      const response = await authApi.get('/auth/user/');
      return response.data;
    } catch (error) {
      console.error('Get user error:', error);
      throw new Error(error.response?.data?.message || error.response?.data?.detail || 'Failed to get user data');
    }
  },

  // Refresh token
  async refreshToken() {
    try {
      const response = await authApi.post('/auth/refresh/');
      const { token } = response.data;
      localStorage.setItem('auth_token', token);
      return token;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Token refresh failed');
    }
  },

  // Password reset request
  async requestPasswordReset(email) {
    try {
      const response = await authApi.post('/auth/password-reset/', { email });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Password reset request failed');
    }
  },

  // Social login (Google, Facebook, Apple)
  async socialLogin(provider, token) {
    try {
      const response = await authApi.post(`/auth/social/${provider}/`, { token });
      const { token: authToken, user } = response.data;
      
      // Store token and user data
      localStorage.setItem('auth_token', authToken);
      localStorage.setItem('user_data', JSON.stringify(user));
      
      return { token: authToken, user };
    } catch (error) {
      throw new Error(error.response?.data?.message || `${provider} login failed`);
    }
  },

  // Check if user is authenticated
  isAuthenticated() {
    const token = localStorage.getItem('auth_token');
    return !!token;
  },

  // Get stored user data
  getStoredUser() {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  },

  // Get stored token
  getStoredToken() {
    return localStorage.getItem('auth_token');
  }
};

export default authService;
