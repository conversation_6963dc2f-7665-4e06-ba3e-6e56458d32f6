import { useState, useEffect } from 'react';
import { getServices, getServiceCategories } from '../services/bookingApi';

const ServicesList = ({ onServiceSelect }) => {
    const [services, setServices] = useState([]);
    const [categories, setCategories] = useState([]);
    const [expandedCategories, setExpandedCategories] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                // Fetch both services and categories in parallel
                const [servicesData, categoriesData] = await Promise.all([
                    getServices(),
                    getServiceCategories()
                ]);
                
                console.log('Services from API:', servicesData);
                console.log('Categories from API:', categoriesData);
                
                setServices(servicesData);
                setCategories(categoriesData);
                
                // Initialize all categories as expanded by default
                const initialExpandedState = {};
                categoriesData.forEach(category => {
                    initialExpandedState[category.id] = true;
                });
                setExpandedCategories(initialExpandedState);
                
                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load services. Using demo data.');
                
                // Fallback to static data if API fails
                const mockCategories = [
                    { id: 1, name: 'Lash Fullset' },
                    { id: 2, name: 'Lash Refill' },
                    { id: 3, name: 'Add-on Services' }
                ];
                
                const mockServices = [
                    {
                        id: 1,
                        category: 1,
                        name: 'Classic Lash Fullset',
                        description: 'One artificial lash applied to one natural lash for a natural look.',
                        price: 199.00,
                        duration: 80
                    },
                    {
                        id: 2,
                        category: 1,
                        name: 'Volume Lash Fullset',
                        description: 'Multiple lashes applied to each natural lash for dramatic volume.',
                        price: 259.00,
                        duration: 140
                    },
                    {
                        id: 3,
                        category: 2,
                        name: '2-Week Lash Refill',
                        description: 'Refresh your lash extensions after 2 weeks.',
                        price: 65,
                        duration: 60
                    }
                ];
                
                setServices(mockServices);
                setCategories(mockCategories);
                
                const initialExpandedState = {};
                mockCategories.forEach(category => {
                    initialExpandedState[category.id] = true;
                });
                setExpandedCategories(initialExpandedState);
            } finally {
                setLoading(false);
            }
        };
        
        fetchData();
    }, []);

    const toggleCategory = (categoryId) => {
        setExpandedCategories(prev => ({
            ...prev,
            [categoryId]: !prev[categoryId]
        }));
    };

    const handleBookService = (service) => {
        if (onServiceSelect) {
            onServiceSelect(service, []);
        }
    };

    const renderServicesByCategory = () => {
        return categories.map(category => {
            const categoryServices = services.filter(service => service.category === category.id);
            
            if (categoryServices.length === 0) return null;
            
            return (
                <div key={category.id} className="mb-8">
                    <div 
                        className="flex items-center justify-between cursor-pointer p-4 bg-gray-100 rounded-lg mb-4"
                        onClick={() => toggleCategory(category.id)}
                    >
                        <h3 className="text-xl font-semibold text-gray-800">{category.name}</h3>
                        <span className="text-gray-600">
                            {expandedCategories[category.id] ? '−' : '+'}
                        </span>
                    </div>
                    
                    {expandedCategories[category.id] && (
                        <div className="space-y-4">
                            {categoryServices.map(service => (
                                <div key={service.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                                    <div className="flex justify-between items-start mb-4">
                                        <div className="flex-1">
                                            <h4 className="text-lg font-semibold text-gray-900 mb-2">
                                                {service.name}
                                            </h4>
                                            <p className="text-gray-600 mb-3">
                                                {service.description}
                                            </p>
                                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                <span className="font-semibold text-primary-600">
                                                    ${service.price}
                                                </span>
                                                {service.duration && (
                                                    <span>Duration: {service.duration} mins</span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="flex justify-end">
                                        <button 
                                            className="btn-primary"
                                            onClick={() => handleBookService(service)}
                                        >
                                            Book Now
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            );
        }).filter(Boolean);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center py-12">
                <div className="text-gray-600">Loading services...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                <p>{error}</p>
            </div>
        );
    }

    return (
        <section className="py-8">
            <div className="container mx-auto px-4">
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Our Services</h2>
                
                {services.length === 0 ? (
                    <p className="text-gray-600">No services available at the moment.</p>
                ) : (
                    renderServicesByCategory()
                )}
            </div>
        </section>
    );
};

export default ServicesList;
