/**
 * Booking API Service
 * This file contains all API calls related to the booking functionality
 * It interfaces with the Django backend APIs
 *
 * Note: These are public endpoints that don't require authentication
 */

import publicApi from '../../../utils/publicApi';

// Log the API base URL for debugging
console.log('Public API Base URL configured as:', publicApi.defaults.baseURL);

// API endpoints based on Django backend structure
const ENDPOINTS = {
    services: '/services/',
    serviceCategories: '/service-categories/',
    employees: '/employees/',
    businessCustomers: '/business-customers/',
    availableTimes: '/businesses/{businessId}/appointments/available-times/',
    booking: '/appointments/',
    businessHours: '/business/hours/'
};

// Helper function for handling API requests using axios
const fetchAPI = async (endpoint, options = {}) => {
    try {
        console.log(`Making API call to: ${endpoint}`);

        let response;
        if (options.method === 'POST') {
            response = await publicApi.post(endpoint, options.body ? JSON.parse(options.body) : {});
        } else {
            response = await publicApi.get(endpoint);
        }

        console.log(`API response from ${endpoint}:`, response.data);
        return response.data;

    } catch (error) {
        console.error('API request failed:', error);

        // If it's a network error, provide fallback data
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
            console.warn('Network error detected, using fallback data');
            return getFallbackData(endpoint);
        }

        throw error;
    }
};

// Fallback data for when API is not available
const getFallbackData = (endpoint) => {
    if (endpoint.includes('/services/')) {
        return [
            {
                id: 1,
                name: 'Classic Lash Extension',
                description: 'Individual lash extensions for a natural look',
                price: 120,
                duration: 90,
                category: 1
            },
            {
                id: 2,
                name: 'Volume Lash Extension',
                description: 'Multiple lashes per natural lash for dramatic volume',
                price: 150,
                duration: 120,
                category: 1
            }
        ];
    }

    if (endpoint.includes('/service-categories/')) {
        return [
            { id: 1, name: 'Lash Extensions' },
            { id: 2, name: 'Lash Maintenance' }
        ];
    }

    if (endpoint.includes('/employees/')) {
        return [
            { id: 1, name: 'Clément', specialties: ['Lash Extensions'] }
        ];
    }

    return [];
};

/**
 * Handle paginated responses by extracting the results array
 * @param {Object} data - The response data from the API
 * @returns {Array} The results array or the original data if not paginated
 */
const handlePaginatedResponse = (data) => {
    // Check if the response is paginated (has results property)
    if (data && data.results && Array.isArray(data.results)) {
        console.log('Received paginated response with', data.results.length, 'items');
        return data.results;
    }

    // If not paginated or unexpected format, return original data
    return data;
};

/**
 * Fetch all available services
 * @returns {Promise} Promise resolving to array of services
 */
export const getServices = async () => {
    try {
        console.log('Fetching services...');
        const data = await fetchAPI(ENDPOINTS.services);

        // Handle paginated or non-paginated response
        if (data && data.results && Array.isArray(data.results)) {
            console.log(`Fetched ${data.results.length} services (paginated)`);
            return data.results;
        } else if (Array.isArray(data)) {
            console.log(`Fetched ${data.length} services (non-paginated)`);
            return data;
        }

        console.log('No services found in response');
        return [];
    } catch (error) {
        console.error('Error fetching services:', error);
        return getFallbackData(ENDPOINTS.services);
    }
};

/**
 * Fetch all service categories
 * @returns {Promise} Promise resolving to array of categories
 */
export const getServiceCategories = async () => {
    try {
        console.log('Fetching service categories...');
        const data = await fetchAPI(ENDPOINTS.serviceCategories);

        // Handle paginated or non-paginated response
        if (data && data.results && Array.isArray(data.results)) {
            console.log(`Fetched ${data.results.length} service categories (paginated)`);
            return data.results;
        } else if (Array.isArray(data)) {
            console.log(`Fetched ${data.length} service categories (non-paginated)`);
            return data;
        }

        console.log('No service categories found in response');
        return [];
    } catch (error) {
        console.error('Error fetching service categories:', error);
        return getFallbackData(ENDPOINTS.serviceCategories);
    }
};

/**
 * Fetch all employees
 * @returns {Promise} Promise resolving to array of employees
 */
export const getEmployees = async () => {
    try {
        console.log('Fetching employees...');
        const data = await fetchAPI(ENDPOINTS.employees);

        // Handle paginated or non-paginated response
        if (data && data.results && Array.isArray(data.results)) {
            console.log(`Fetched ${data.results.length} employees (paginated)`);
            return data.results;
        } else if (Array.isArray(data)) {
            console.log(`Fetched ${data.length} employees (non-paginated)`);
            return data;
        }

        console.log('No employees found in response');
        return [];
    } catch (error) {
        console.error('Error fetching employees:', error);
        return getFallbackData(ENDPOINTS.employees);
    }
};

/**
 * Fetch available time slots for a specific date
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {number} serviceId - ID of the selected service
 * @param {number} employeeId - ID of the selected employee (optional)
 * @param {number} businessId - ID of the business (default: 1)
 * @returns {Promise} Promise resolving to array of available time slots
 */
export const getAvailableTimeSlots = async (date, serviceId, employeeId, businessId = 1) => {
    let endpoint = `/appointments/${businessId}/available-times/?date=${date}&service_id=${serviceId}`;

    if (employeeId) {
        endpoint += `&employee_id=${employeeId}`;
    }

    return fetchAPI(endpoint);
};

/**
 * Fetch available time slots for all employees in a business
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {number} serviceId - ID of the selected service
 * @param {number} businessId - ID of the business
 * @returns {Promise} Promise resolving to availability grouped by employee
 */
export const getAvailableTimeSlotsForBusiness = async (date, serviceId, businessId) => {
    let endpoint = `/businesses/${businessId}/appointments/available-times/?date=${date}&service_id=${serviceId}`;
    return fetchAPI(endpoint);
};

/**
 * Create a new booking
 * @param {Object} bookingData - Booking information
 * @returns {Promise} Promise resolving to created booking
 */
export const createBooking = async (bookingData) => {
    try {
        // Format the data for the API (matching original format)
        const formattedData = {
            service_id: bookingData.service,
            employee_id: bookingData.employee,
            date: bookingData.date,
            time: bookingData.time,
            add_on_ids: bookingData.addOns || [],
            customer: {
                name: bookingData.customer?.name || bookingData.name,
                email: bookingData.customer?.email || bookingData.email,
                phone: bookingData.customer?.phone || bookingData.phone,
                address: bookingData.customer?.address || '',
                address2: bookingData.customer?.address2 || ''
            },
            notes: bookingData.notes || '',
            payment_method: bookingData.payment?.holdWithCard ? 'card_hold' : 'at_appointment'
        };

        console.log('Sending booking data to API:', formattedData);

        return await fetchAPI(ENDPOINTS.booking, {
            method: 'POST',
            body: JSON.stringify(formattedData)
        });
    } catch (error) {
        console.error('Error creating booking:', error);
        throw error;
    }
};

/**
 * Get business operating hours
 * @returns {Promise} Promise resolving to business hours
 */
export const getBusinessHours = async () => {
    return fetchAPI(ENDPOINTS.businessHours);
};
