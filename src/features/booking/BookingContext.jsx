import { createContext, useContext, useState, useEffect } from 'react'

const BookingContext = createContext()

const BOOKING_STORAGE_KEY = 'currentBookingData'

// Initial booking state structure
const initialBookingState = {
  selectedService: null,
  selectedEmployee: null,
  selectedDate: null,
  selectedTime: null,
  selectedAddOns: [],
  customerInfo: {
    name: '',
    email: '',
    phone: '',
    address1: '',
    address2: '',
    creditCardName: '',
    creditCardNumber: '',
    creditCardExpiry: '',
    creditCardCVV: '',
    agree: false
  },
  consentData: {
    signature: '',
    consentAgreed: false,
    consentTimestamp: null
  },
  step: 'service-selection', // 'service-selection', 'time-selection', 'consent', 'review', 'confirmation'
  totalCost: 0
}

export function BookingProvider({ children }) {
  const [bookingData, setBookingData] = useState(initialBookingState)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Load booking data from session storage on mount
  useEffect(() => {
    try {
      const storedData = sessionStorage.getItem(BOOKING_STORAGE_KEY)
      if (storedData) {
        const parsedData = JSON.parse(storedData)
        setBookingData(prev => ({ ...prev, ...parsedData }))
      }
    } catch (error) {
      console.error('Error loading booking data from storage:', error)
      // Clear corrupted data
      sessionStorage.removeItem(BOOKING_STORAGE_KEY)
    }
  }, [])

  // Save booking data to session storage whenever it changes
  useEffect(() => {
    try {
      sessionStorage.setItem(BOOKING_STORAGE_KEY, JSON.stringify(bookingData))
    } catch (error) {
      console.error('Error saving booking data to storage:', error)
    }
  }, [bookingData])

  // Update specific booking fields
  const updateBookingData = (updates) => {
    setBookingData(prev => {
      const newData = { ...prev, ...updates }

      // Recalculate total cost when service or add-ons change
      if (updates.selectedService || updates.selectedAddOns) {
        const serviceCost = parseFloat(newData.selectedService?.price || 0)
        const addOnsCost = newData.selectedAddOns?.reduce((sum, addon) =>
          sum + parseFloat(addon.price || 0), 0) || 0
        newData.totalCost = serviceCost + addOnsCost
      }

      return newData
    })
  }

  // Set service and add-ons
  const setServiceAndAddOns = (service, addOns = []) => {
    updateBookingData({
      selectedService: service,
      selectedAddOns: addOns,
      step: 'time-selection'
    })
  }

  // Set employee and time
  const setEmployeeAndTime = (employee, date, time) => {
    updateBookingData({
      selectedEmployee: employee,
      selectedDate: date,
      selectedTime: time,
      step: 'consent'
    })
  }

  // Set consent data
  const setConsentData = (consentData) => {
    updateBookingData({
      consentData: {
        ...bookingData.consentData,
        ...consentData,
        consentTimestamp: new Date().toISOString()
      },
      step: 'review'
    })
  }

  // Set customer info
  const setCustomerInfo = (customerInfo) => {
    updateBookingData({
      customerInfo: {
        ...bookingData.customerInfo,
        ...customerInfo
      }
    })
  }

  // Clear booking data (after successful booking or cancellation)
  const clearBookingData = () => {
    setBookingData(initialBookingState)
    sessionStorage.removeItem(BOOKING_STORAGE_KEY)
  }

  // Check if booking is complete enough for specific steps
  const canProceedToConsent = () => {
    return bookingData.selectedService &&
           bookingData.selectedEmployee &&
           bookingData.selectedDate &&
           bookingData.selectedTime
  }

  const canProceedToReview = () => {
    return canProceedToConsent() &&
           bookingData.consentData.consentAgreed
  }

  const canProceedToConfirmation = () => {
    return canProceedToReview() &&
           bookingData.customerInfo.name &&
           bookingData.customerInfo.email &&
           bookingData.customerInfo.phone
  }

  // Get formatted booking data for API submission
  const getBookingDataForAPI = () => {
    return {
      service: bookingData.selectedService?.id,
      employee: bookingData.selectedEmployee?.id,
      date: bookingData.selectedDate,
      time: bookingData.selectedTime,
      addOns: bookingData.selectedAddOns?.map(addon => addon.id) || [],
      customer: {
        name: bookingData.customerInfo.name,
        email: bookingData.customerInfo.email,
        phone: bookingData.customerInfo.phone,
        address: bookingData.customerInfo.address1,
        address2: bookingData.customerInfo.address2
      },
      consent: bookingData.consentData,
      totalCost: bookingData.totalCost
    }
  }

  return (
    <BookingContext.Provider value={{
      bookingData,
      loading,
      error,
      updateBookingData,
      setServiceAndAddOns,
      setEmployeeAndTime,
      setConsentData,
      setCustomerInfo,
      clearBookingData,
      canProceedToConsent,
      canProceedToReview,
      canProceedToConfirmation,
      getBookingDataForAPI
    }}>
      {children}
    </BookingContext.Provider>
  )
}

export function useBooking() {
  const context = useContext(BookingContext)
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider')
  }
  return context
}
