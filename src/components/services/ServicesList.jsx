import { useState, useEffect } from 'react';
import { getServices, getServiceCategories } from '../../api/bookingApi';
import ServiceItem from './ServiceItem.jsx';
import ServiceAddOnOverlay from './ServiceAddOnOverlay.jsx';
import './Services.css';

const ServicesList = ({ onServiceSelect }) => {
    const [services, setServices] = useState([]);
    const [categories, setCategories] = useState([]);
    const [expandedCategories, setExpandedCategories] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedService, setSelectedService] = useState(null);
    const [showAddOnOverlay, setShowAddOnOverlay] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                // Fetch both services and categories in parallel
                const [servicesData, categoriesData] = await Promise.all([
                    getServices(),
                    getServiceCategories()
                ]);
                
                console.log('Services from API:', servicesData);
                console.log('Categories from API:', categoriesData);
                
                // Process and organize services by category if needed
                setServices(servicesData);
                setCategories(categoriesData);
                
                // Initialize all categories as collapsed by default
                const initialExpandedState = {};
                categoriesData.forEach(category => {
                    initialExpandedState[category.id] = false;
                });
                setExpandedCategories(initialExpandedState);
                
                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load services. Please try again later.');
                
                // Fallback to static data if API fails
                const mockCategories = [
                    { id: 1, name: 'Lash Fullset' },
                    { id: 2, name: 'Lash Refill' },
                    { id: 3, name: 'Add-on Services' }
                ];
                
                const mockServices = [
                    {
                        id: 1,
                        category: 1,
                        name: 'Classic Lash Fullset(C01-C02)',
                        description: 'Service Duration: Around 80mins. One artificial lash will be applied to one natural lash in the classic eyelash extension technique provided by this service.',
                        price: 199.00,
                        image: '/images/classic-lash.jpg'
                    },
                    {
                        id: 2,
                        category: 1,
                        name: 'Styling Lash Fullset(S01-S08)',
                        description: 'Service Duration: 100-120mins. To create various styles(Thai,Anime,Ms Clement,Sunflower,...), this service will apply multiple lashes with a closed fan to one natural lash during the eyelash extension procedure.',
                        price: 229.00,
                        image: '/images/styling-lash.jpg'
                    },
                    {
                        id: 3,
                        category: 1,
                        name: 'Volume Fullset(Non-Mega)',
                        description: 'Service Duration: 120-140 minutes. This service involves applying multiple lashes, using both closed and open fans, to each natural lash to create various styles and achieve a fuller, more dramatic look.',
                        price: 259.00,
                        image: '/images/volume-lash.jpg'
                    },
                    {
                        id: 4,
                        category: 1,
                        name: 'Premium Clément Real Mink Fullset(P01-P02)',
                        description: 'Service Duration: around 90mins. For the most natural look, this service will apply one premium Clement real mink lash(100% cruelty-free) to each natural lash during the eyelash extension process.',
                        price: 279.00,
                        image: '/images/premium-lash.jpg'
                    },
                    {
                        id: 5,
                        category: 2,
                        name: '2-Week Lash Refill',
                        description: 'Refresh your lash extensions after 2 weeks.',
                        price: 65,
                        duration: 60,
                        note: 'Only on Clement\'s work'
                    },
                    {
                        id: 6,
                        category: 2,
                        name: '3-Week Lash Refill',
                        description: 'Refresh your lash extensions after 3 weeks.',
                        price: 75,
                        duration: 75,
                        note: 'Only on Clement\'s work'
                    },
                    {
                        id: 7,
                        category: 3,
                        name: 'Bottom Lash',
                        description: 'This service will complete your eyelash look by applying one artificial lash to one of your natural lower lashes, to match the upper lash.',
                        price: 29,
                        duration: 30
                    },
                    {
                        id: 8,
                        category: 3,
                        name: 'Lash Removal',
                        description: '15 minutes. This service will remove all the faux lashes and clean the entire eye area.',
                        price: 0,
                        duration: 15
                    }
                ];
                
                // Initialize all mock categories as collapsed by default
                const initialExpandedState = {};
                mockCategories.forEach(category => {
                    initialExpandedState[category.id] = false;
                });
                
                setServices(mockServices);
                setCategories(mockCategories);
                setExpandedCategories(initialExpandedState);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const toggleCategory = (categoryId) => {
        setExpandedCategories(prev => ({
            ...prev,
            [categoryId]: !prev[categoryId]
        }));
    };

    const handleBookService = (service) => {
        setSelectedService(service);
        setShowAddOnOverlay(true);
    };

    const handleCloseOverlay = () => {
        setShowAddOnOverlay(false);
    };

    const handleContinueBooking = (service, addOns) => {
        // Here we would normally proceed to the actual booking form
        console.log('Continuing with booking:', { service, addOns });
        setShowAddOnOverlay(false);
        // Pass the service and selected add-ons to the parent component
        onServiceSelect(service, addOns);
    };

    // Group services by category
    const renderServicesByCategory = () => {
        // When logging the service data to check what categories we have
        console.log('Current categories:', categories);
        console.log('Current services:', services);

        // Make sure we're properly matching service categories to services
        const categoryMapping = {};
        services.forEach(service => {
            const categoryId = service.category;
            if (!categoryMapping[categoryId]) {
                categoryMapping[categoryId] = [];
            }
            categoryMapping[categoryId].push(service);
        });

        console.log('Category mapping:', categoryMapping);

        // Render all categories, even if they might not have a direct match in our filter
        return categories.map(category => {
            // Try to find services for this category
            const categoryServices = services.filter(service =>
                // Match by either id match or string match
                service.category === category.id ||
                service.category === category.id.toString() ||
                // Also check for category match by name if needed
                (service.category_name && service.category_name.toLowerCase() === category.name.toLowerCase())
            );

            console.log(`Category ${category.name} has ${categoryServices.length} services`);

            // Skip categories with no services
            if (categoryServices.length === 0) return null;

            const isExpanded = expandedCategories[category.id];

            return (
                <div
                    key={category.id}
                    className={`service-category ${isExpanded ? '' : 'collapsed'}`}
                >
                    <h3
                        className="category-name"
                        onClick={() => toggleCategory(category.id)}
                    >
                        {category.name}
                    </h3>
                    <div className="category-content">
                        {categoryServices.map(service => (
                            <div key={service.id} className="service-item">
                                <div className="service-item-header">
                                    <h4 className="service-name">{service.name}</h4>
                                    <div className="service-price">
                                        ${typeof service.price === 'number'
                                            ? service.price.toFixed(2)
                                            : parseFloat(service.price).toFixed(2)}
                                    </div>
                                </div>
                                <p className="service-description">{service.description}</p>

                                {service.note && (
                                    <p className="service-note">Note: {service.note}</p>
                                )}

                                <div className="service-meta">
                                    {service.duration && (
                                        <p className="duration">Duration: {service.duration} mins</p>
                                    )}
                                    <button
                                        className="book-service-btn"
                                        onClick={() => handleBookService(service)}
                                    >
                                        Book Now
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            );
        }).filter(Boolean); // Remove null entries
    };

    if (loading) {
        return <div className="services-loading">Loading services...</div>;
    }

    if (error) {
        return (
            <div className="services-error">
                <p>{error}</p>
                <p>Using demo data instead.</p>
            </div>
        );
    }

    return (
        <section id="services" className="services-section">
            <h2>Services</h2>

            {services.length === 0 ? (
                <p>No services available at the moment.</p>
            ) : renderServicesByCategory()}

            {showAddOnOverlay && selectedService && (
                <ServiceAddOnOverlay
                    service={selectedService}
                    onClose={handleCloseOverlay}
                    onContinue={handleContinueBooking}
                />
            )}
        </section>
    );
};

export default ServicesList;
