import { useRef, useState, useEffect } from 'react';

/**
 * Lightweight signature pad (no external dependency).
 * – Renders a canvas the user can draw on (mouse / touch).
 * – Exposes the signature as PNG data URL via `onChange` every time the drawing stops.
 * – `value` prop (optional) can be used to load an existing signature.
 */
function SignaturePad({ width = '100%', height = 200, value = null, onChange }) {
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasDims, setCanvasDims] = useState({ w: 0, h: 0 });

  // Initialise canvas size and settings
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const updateSize = () => {
      const parent = canvas.parentElement;
      const newW = parent ? parent.clientWidth : 600;
      const newH = height;
      setCanvasDims({ w: newW, h: newH });
      canvas.width = newW * 2; // HiDPI: 2x for crisp lines
      canvas.height = newH * 2;
      canvas.style.width = `${newW}px`;
      canvas.style.height = `${newH}px`;
      const ctx = canvas.getContext('2d');
      ctx.scale(2, 2);
      ctx.lineWidth = 2;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.strokeStyle = '#000';

      // Redraw existing signature if present
      if (value) {
        const img = new Image();
        img.onload = () => ctx.drawImage(img, 0, 0, newW, newH);
        img.src = value;
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [height, value]);

  const getPos = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const clientX = e.touches ? e.touches[0].clientX : e.clientX;
    const clientY = e.touches ? e.touches[0].clientY : e.clientY;
    return {
      x: clientX - rect.left,
      y: clientY - rect.top,
    };
  };

  const start = (e) => {
    e.preventDefault();
    const ctx = canvasRef.current.getContext('2d');
    const pos = getPos(e);
    ctx.beginPath();
    ctx.moveTo(pos.x, pos.y);
    setIsDrawing(true);
  };

  const draw = (e) => {
    if (!isDrawing) return;
    e.preventDefault();
    const ctx = canvasRef.current.getContext('2d');
    const pos = getPos(e);
    ctx.lineTo(pos.x, pos.y);
    ctx.stroke();
  };

  const end = (e) => {
    if (!isDrawing) return;
    e.preventDefault();
    setIsDrawing(false);
    const dataUrl = canvasRef.current.toDataURL('image/png');
    onChange?.(dataUrl);
  };

  const handleClear = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    onChange?.('');
  };

  return (
    <div className="space-y-2">
      <canvas
        ref={canvasRef}
        className="border border-gray-400 w-full"
        style={{ touchAction: 'none' }}
        onMouseDown={start}
        onMouseMove={draw}
        onMouseUp={end}
        onMouseLeave={end}
        onTouchStart={start}
        onTouchMove={draw}
        onTouchEnd={end}
      />
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleClear}
          className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded"
        >
          Clear
        </button>
      </div>
    </div>
  );
}

export default SignaturePad; 