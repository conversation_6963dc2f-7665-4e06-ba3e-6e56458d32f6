import './Layout.css';

const Navigation = ({ activeTab, onTabChange }) => {
    const handleTabClick = (tab) => {
        if (onTabChange) {
            onTabChange(tab);
        }
    };
    
    return (
        <nav className="main-nav">
            <ul>
                <li className={activeTab === 'services' ? 'active' : ''}>
                    <a 
                        href="#services" 
                        onClick={(e) => {
                            e.preventDefault();
                            handleTabClick('services');
                        }}
                    >
                        Services
                    </a>
                </li>
                <li className={activeTab === 'booking' ? 'active' : ''}>
                    <a 
                        href="#booking" 
                        onClick={(e) => {
                            e.preventDefault();
                            handleTabClick('booking');
                        }}
                    >
                        Book Now
                    </a>
                </li>
            </ul>
        </nav>
    );
};

export default Navigation;
