import { useEffect, useState } from 'react';
import Header from './Header.jsx';
import Navigation from './Navigation.jsx';
import Footer from './Footer.jsx';
import './Layout.css';

const Layout = ({ children, businessInfo, activeTab, onTabChange }) => {
    const [scrollPosition, setScrollPosition] = useState(0);
    
    // Handle scroll event to update navigation
    useEffect(() => {
        const handleScroll = () => {
            const position = window.scrollY;
            setScrollPosition(position);
        };
        
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);
    
    return (
        <div className="layout">
            <Header businessInfo={businessInfo} />
            <Navigation activeTab={activeTab} onTabChange={onTabChange} />
            <main className="main-content">
                {children}
            </main>
            <Footer businessInfo={businessInfo} />
        </div>
    );
};

export default Layout;
