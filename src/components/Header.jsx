import { useAuth } from '../features/auth/AuthContext'

function Header() {
  const { user, logout } = useAuth()

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold text-gray-900">Clément Lash</h1>
          <span className="text-gray-600">Bellevue, WA</span>
        </div>
        
        {user && (
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">Welcome, {user.user?.name || 'User'}</span>
            <button
              onClick={logout}
              className="text-sm text-gray-600 hover:text-gray-900"
            >
              Logout
            </button>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
