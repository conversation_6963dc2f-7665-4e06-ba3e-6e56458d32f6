/* Appointment Summary Sidebar Component */
.appointment-summary-sidebar {
    background-color: #f5f5f5;
    border-left: 1px solid #e0e0e0;
    padding: 30px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    width: 375px; /* Mobile device width */
    flex-shrink: 0;
}

.appointment-summary-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Order Summary Title */
.order-summary-title {
    margin-bottom: 25px;
}

.order-summary-title h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Date and Time Row */
.appointment-datetime-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 20px;
}

.appointment-date {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.appointment-time {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

/* Stylist Info Row */
.stylist-info-row {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 20px;
}

.stylist-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 20px;
}

.stylist-details {
    flex: 1;
}

.stylist-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.service-name {
    font-size: 14px;
    color: #666;
}

.service-price {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Separator Line */
.separator-line {
    height: 1px;
    background-color: #e0e0e0;
    margin: 20px 0;
}

/* Services Section */
.services-section {
    margin-bottom: 20px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.service-item-name {
    font-size: 14px;
    color: #666;
}

.service-item-price {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-top: 10px;
}

.total-label {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.total-amount {
    font-size: 18px;
    font-weight: 700;
    color: #333;
}

/* Payment Summary */
.payment-summary {
    margin-bottom: 30px;
}

.payment-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.payment-label {
    font-size: 16px;
    color: #666;
}

.payment-amount {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}



/* Mobile Responsive */
@media (max-width: 768px) {
    .appointment-summary-sidebar {
        position: static;
        border-left: none;
        border-top: 1px solid #e0e0e0;
        padding: 20px;
        margin-top: 20px;
        min-height: auto;
    }

    .appointment-datetime-row {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .appointment-date,
    .appointment-time {
        font-size: 20px;
    }

    .stylist-info-row {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .stylist-avatar {
        align-self: center;
    }

    .stylist-details {
        text-align: center;
    }
}
