/* Collapsible Order Summary for Mobile */
.collapsible-order-summary {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 0;
    margin: 0;
    width: 100%;
}

.order-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    min-height: 60px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-icon {
    display: flex;
    align-items: center;
    color: #333;
}

.cart-icon svg {
    width: 20px;
    height: 20px;
}

.header-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.header-right {
    display: flex;
    align-items: center;
}

.chevron-icon {
    font-size: 14px;
    color: #666;
    transition: transform 0.2s ease;
}

.order-summary-content {
    padding: 20px;
    background-color: #f5f5f5;
}

/* Date and Time Row */
.appointment-datetime-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 15px;
}

.appointment-date {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.appointment-time {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Stylist Info Row */
.stylist-info-row {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    margin-bottom: 15px;
}

.stylist-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.stylist-details {
    flex: 1;
}

.stylist-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.service-name {
    font-size: 14px;
    color: #666;
}

.service-price {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Separator Line */
.separator-line {
    height: 1px;
    background-color: #d0d0d0;
    margin: 15px 0;
}

/* Services Section */
.services-section {
    margin-bottom: 15px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.service-item-name {
    font-size: 14px;
    color: #333;
}

.service-item-price {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0 10px 0;
    border-top: 1px solid #d0d0d0;
    margin-top: 10px;
}

.total-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.total-amount {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Payment Summary */
.payment-summary {
    margin-top: 15px;
}

.payment-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.payment-row:first-child .payment-label {
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-row:first-child .payment-label::after {
    content: "Hold with Card";
    background-color: #e0e0e0;
    color: #666;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.payment-label {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.payment-amount {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
    .appointment-datetime-row {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .appointment-date,
    .appointment-time {
        font-size: 16px;
    }

    .stylist-info-row {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .stylist-avatar {
        align-self: center;
    }

    .stylist-details {
        text-align: center;
    }
}
