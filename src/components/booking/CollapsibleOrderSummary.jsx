import { useState } from 'react';
import './CollapsibleOrderSummary.css';

const CollapsibleOrderSummary = ({ 
    selectedService, 
    selectedEmployee, 
    selectedDate, 
    selectedTime, 
    selectedAddOns = [],
    totalCost 
}) => {
    const [isExpanded, setIsExpanded] = useState(false);

    // Format date
    const formattedDate = selectedDate ? new Date(selectedDate).toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }) : '';
    
    // Format time
    const formatTime = (timeStr) => {
        if (!timeStr) return '';
        
        try {
            const [hours, minutes] = timeStr.split(':');
            const hour = parseInt(hours, 10);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12;
            return `${hour12}:${minutes} ${ampm}`;
        } catch (error) {
            console.error('Error formatting time:', error);
            return timeStr;
        }
    };

    const toggleExpanded = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <div className="collapsible-order-summary">
            <div className="order-summary-header" onClick={toggleExpanded}>
                <div className="header-left">
                    <div className="cart-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="9" cy="21" r="1"></circle>
                            <circle cx="20" cy="21" r="1"></circle>
                            <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                        </svg>
                    </div>
                    <span className="header-text">View Order Summary</span>
                </div>
                <div className="header-right">
                    <span className="chevron-icon">{isExpanded ? '▲' : '▼'}</span>
                </div>
            </div>

            {isExpanded && (
                <div className="order-summary-content">
                    {/* Date and Time Row */}
                    <div className="appointment-datetime-row">
                        <div className="appointment-date">{formattedDate}</div>
                        <div className="appointment-time">{formatTime(selectedTime)}</div>
                    </div>

                    {/* Stylist Info Row */}
                    <div className="stylist-info-row">
                        <img 
                            src={selectedEmployee?.profile_image || '/images/default-avatar.png'} 
                            alt={selectedEmployee?.display_name || 'Service Provider'} 
                            className="stylist-avatar"
                        />
                        <div className="stylist-details">
                            <div className="stylist-name">
                                ({selectedEmployee?.title || 'Master Stylist'}) {selectedEmployee?.display_name?.split(' ')[0] || 'Se'}..
                            </div>
                            <div className="service-name">{selectedService?.name}</div>
                        </div>
                        <div className="service-price">${parseFloat(selectedService?.price || 0).toFixed(2)}</div>
                    </div>

                    {/* Grey Separator */}
                    <div className="separator-line"></div>

                    {/* Services and Add-ons */}
                    <div className="services-section">
                        {selectedAddOns && selectedAddOns.map(addon => (
                            <div key={addon.id} className="service-item">
                                <div className="service-item-name">{addon.name}</div>
                                <div className="service-item-price">${parseFloat(addon.price).toFixed(2)}</div>
                            </div>
                        ))}
                        
                        <div className="total-row">
                            <div className="total-label">Total</div>
                            <div className="total-amount">${totalCost?.toFixed(2) || '0.00'}</div>
                        </div>
                    </div>

                    {/* Grey Separator */}
                    <div className="separator-line"></div>

                    {/* Payment Summary */}
                    <div className="payment-summary">
                        <div className="payment-row">
                            <div className="payment-label">Total Due Now</div>
                            <div className="payment-amount">$0.00</div>
                        </div>
                        <div className="payment-row">
                            <div className="payment-label">Total Due at Business</div>
                            <div className="payment-amount">${totalCost?.toFixed(2) || '0.00'}</div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CollapsibleOrderSummary;
