import { useState, useEffect } from 'react';
import { useBooking } from '../features/booking/BookingContext';
import ServicesList from './services/ServicesList.jsx';
import BookingPage from './booking/BookingPage.jsx';

const MainContentWrapper = ({ 
    services, 
    loading, 
    activeTab, 
    setActiveTab,
    selectedService,
    setSelectedService,
    selectedAddOns,
    setSelectedAddOns
}) => {
    const { setServiceAndAddOns } = useBooking();

    const handleServiceSelect = (service, addOns = []) => {
        console.log('Service selected with add-ons:', service, addOns);
        
        // Update local state (for App.jsx compatibility)
        setSelectedService(service);
        setSelectedAddOns(addOns);
        
        // Update booking context (for persistence)
        setServiceAndAddOns(service, addOns);
        
        // Automatically switch to booking tab when a service is selected
        setActiveTab('booking');
    };

    if (loading) {
        return <div className="loading-screen">Loading services...</div>;
    }

    return (
        <>
            {activeTab === 'services' ? (
                <ServicesList
                    services={services}
                    onServiceSelect={handleServiceSelect}
                />
            ) : (
                <BookingPage
                    selectedService={selectedService}
                    addOns={selectedAddOns}
                />
            )}
        </>
    );
};

export default MainContentWrapper;
