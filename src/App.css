/* App.css - Main application styles */

.app-container {
    min-height: 100vh;
    background-color: #f9f9f9;
}

.loading-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 1.2rem;
    color: #666;
}

/* Global styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f9f9f9;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
        monospace;
}

/* Layout styles */
.layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        padding: 10px;
    }
}
